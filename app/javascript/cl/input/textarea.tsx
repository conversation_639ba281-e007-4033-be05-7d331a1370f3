import { useControlled } from "@/hooks/use-controlled";
import type { HelpfulOmit } from "@/types/util";
import clsx from "clsx";
import type { ComponentPropsWithoutRef } from "react";
import React, { forwardRef } from "react";

export interface TextareaProps
  extends HelpfulOmit<ComponentPropsWithoutRef<"textarea">, "value" | "defaultValue" | "onChange"> {
  value?: string;
  defaultValue?: string;
  onChange?: (event: React.ChangeEvent<HTMLTextAreaElement>, value: string) => void;
  showCharCounter?: boolean;
  hasError?: boolean;
}

export const Textarea = forwardRef<HTMLTextAreaElement, TextareaProps>(
  (
    { value, defaultValue = "", onChange, showCharCounter = false, hasError = false, maxLength, className, ...props },
    ref
  ) => {
    const controlledProps = useControlled({
      value,
      defaultValue,
      onChange,
      getValue: (event) => event.currentTarget.value,
    });

    const hasMaxLength = maxLength !== undefined;
    const isOverLimit = hasMaxLength && controlledProps.value.length > maxLength;
    const shouldShowCounter = showCharCounter && hasMaxLength;

    return (
      <div className="cl-textarea-container">
        <textarea
          {...props}
          ref={ref}
          {...controlledProps}
          className={clsx("cl-textarea", hasError && "cl-textarea-error", className)}
        />
        {shouldShowCounter && (
          <div className={clsx("cl-textarea-counter", isOverLimit && "cl-textarea-counter-error")}>
            {controlledProps.value.length} / {maxLength}
          </div>
        )}
      </div>
    );
  }
);

Textarea.displayName = "Textarea";
