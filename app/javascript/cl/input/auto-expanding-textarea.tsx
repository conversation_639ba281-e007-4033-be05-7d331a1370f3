import React, { useEffect, useRef } from "react";
import { Textarea, type TextareaProps } from "./textarea";
import type { HelpfulOmit } from "@/types/util";
import clsx from "clsx";

interface AutoExpandingTextareaProps extends HelpfulOmit<TextareaProps, "onInput"> {
  rowLimit: number;
}

export function AutoExpandingTextarea(props: AutoExpandingTextareaProps) {
  const { rowLimit, className, ...rest } = props;
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const handleInput = useHandleInput(textareaRef, rowLimit);

  // Reset height when prompt is empty
  useEffect(() => {
    if (textareaRef.current && props.value === "") textareaRef.current.style.height = "auto";
  }, [props.value]);

  return (
    <Textarea ref={textareaRef} onInput={handleInput} {...rest} className={clsx("cl-auto-expanding", className)} />
  );
}

const useHandleInput = (textareaRef: React.RefObject<HTMLTextAreaElement>, rowLimit: number) => {
  const element = textareaRef.current;
  if (!element) return;

  return () => {
    element.style.height = "auto";

    const lineHeight = parseFloat(getComputedStyle(element).lineHeight);
    const maxHeight = lineHeight * rowLimit;

    if (element.scrollHeight < maxHeight) {
      element.style.height = `${element.scrollHeight}px`;
    } else {
      element.style.height = `${maxHeight}px`;
    }
  };
};
