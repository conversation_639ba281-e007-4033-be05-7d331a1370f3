@use "@styles/cl/core/colors";

$border-color: lightgrey;
$white: colors.$md-white;
$primary-color: colors.$md-primary;
$gradient: linear-gradient(to right, $primary-color, #18d2e8);

@mixin custom-scroll {
  overflow-y: auto;

  scrollbar-width: thin;
  scrollbar-color: #edf0f2 $white;

  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-track {
    background: $white;
    border-radius: 10px;
  }

  &::-webkit-scrollbar-thumb {
    background: #edf0f2;
    border-radius: 10px;
  }
}

@mixin chat-message {
  border-radius: 4px;
  padding: 10px;
  word-break: break-word;
}

.chat-bot-wrapper {
  position: fixed;
  top: 40%;
  z-index: 10;
  right: 10px;
  font-size: 13px;

  .toggle-container {
    display: inline-block;
    text-align: center;

    .toggler {
      position: relative;
      z-index: 10;
    }

    .toggler-text {
      font-weight: 800;
      color: $primary-color;
    }
  }

  .chat-bot-container {
    position: absolute;
    top: 30px;
    right: 0;
    width: 350px;
    height: 450px;
    background: $gradient;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    border-radius: 7px;
    padding: 2px;

    .chat-bot {
      &-header {
        display: flex;
        align-items: baseline;
        padding: 10px;
        height: 60px;

        &-text {
          flex: 1;
          color: $white;
        }

        .back-button {
          height: 17px;
          width: 17px;
          padding: 0 !important;
          line-height: 1rem;
          margin-right: 10px;
          border: none;
          border-radius: 50%;
        }

        .new-chat-button {
          display: flex;
          align-items: center;
          gap: 5px;
          align-self: end;
          color: $primary-color;
          background: $white;
          border: none;
          padding: 2px 10px;
          border-radius: 20px;

          span {
            font-size: 11px;
            font-weight: 700;
          }
        }
      }

      &-card {
        height: calc(100% - 60px);
        background: $white;
        border-radius: inherit;
        display: flex;
        flex-direction: column;
      }

      &-body {
        @include custom-scroll;

        flex: 1;
        padding: 10px;

        .chat-list {
          &-item {
            height: 30px;
            padding: 5px 10px;
            display: flex;
            justify-content: space-between;
            border-radius: 4px;

            .elapsed-time {
              color: colors.$link-plain-color;
            }

            button {
              flex: 1;
              text-align: left;
            }

            &:hover {
              background: $gradient;
              color: $white;

              .elapsed-time {
                color: $white;
              }
            }
          }
        }

        .chat-messages {
          display: flex;
          flex-direction: column;

          p {
            margin: 0;
          }

          .user-message {
            @include chat-message;

            display: inline-block;
            align-self: flex-end;
            background: $gradient;
            color: $white;
          }

          .assistant-message {
            @include chat-message;

            align-self: flex-start;
            display: flex;
            align-items: baseline;
            gap: 10px;
            padding-left: 0;
          }
        }
      }

      &-footer {
        padding: 10px;

        form {
          display: flex;
          align-items: center;
          padding: 5px;
          gap: 3px;
          border: 1px solid $border-color;
          border-radius: 4px;

          textarea {
            @include custom-scroll;

            border: none;

            &:focus {
              border: none !important;
            }
          }

          button {
            border-radius: 50%;
            height: 30px;
            width: 30px;
            padding: initial;
          }
        }
      }
    }
  }
}
