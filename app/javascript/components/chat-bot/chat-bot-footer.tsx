import React, { useState } from "react";
import { useAppDispatch, useAppSelector } from "@/hooks/redux";
import { continueChat, createNewChat, selectChatId, selectIsStreaming } from "@/slices/chat-bot";
import { AutoExpandingTextarea } from "@/cl/input/auto-expanding-textarea";
import { Button } from "@/cl/button";
import { Icon } from "@/cl/icon";
import { t } from "@/i18n";
import styles from "./index.module.scss";

export default function ChatBotFooter() {
  const isStreaming = useAppSelector(selectIsStreaming);
  const { prompt, setPrompt, handleSubmit } = useSubmitPrompt();

  const onKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter" && !e.shiftKey) return handleSubmit(e);
  };

  return (
    <div className={styles.chatBotFooter}>
      <form onSubmit={handleSubmit}>
        <div style={{ flex: 1 }}>
          <AutoExpandingTextarea
            value={prompt}
            rows={1}
            rowLimit={5}
            placeholder={t("ask_me_anything", { ns: "llm" })}
            aria-label={t("type_your_message", { ns: "llm" })}
            onChange={(e) => setPrompt(e.target.value)}
            onKeyDown={onKeyDown}
            disabled={isStreaming}
          />
        </div>
        <Button title="send" brand="primary" type="submit" disabled={isStreaming || !prompt.trim()}>
          <Icon name="arrow-right" />
        </Button>
      </form>
    </div>
  );
}

const useSubmitPrompt = () => {
  const chatId = useAppSelector(selectChatId);
  const [prompt, setPrompt] = useState("");
  const dispatch = useAppDispatch();

  const handleSubmit = (e: React.FormEvent<HTMLFormElement> | React.KeyboardEvent<HTMLTextAreaElement>) => {
    e.preventDefault();
    if (!prompt.trim()) return;

    if (!chatId) {
      void dispatch(createNewChat({ prompt }));
    } else {
      void dispatch(continueChat({ prompt, chatId }));
    }
    setPrompt("");
  };

  return { prompt, setPrompt, handleSubmit };
};
