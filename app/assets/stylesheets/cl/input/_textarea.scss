@import "../core/colors";

.cl-textarea-container {
  position: relative;
}

.cl-textarea {
  display: block;
  width: 100%;
  padding: 12px;
  border: 1px solid $md-input-border;
  border-radius: 4px;
  font-size: 14px;
  font-family: inherit;
  resize: vertical;
  min-height: 100px;
  transition: border-color 0.2s ease;

  &:focus {
    outline: none;
    border-color: $md-primary;
    box-shadow: 0 0 0 2px rgba($md-primary, 0.25);
  }

  &.cl-textarea-error {
    border-color: $md-error-fg !important;

    &:focus {
      border-color: $md-error-fg !important;
      box-shadow: 0 0 0 2px rgba($md-error-fg, 0.25) !important;
    }
  }

  &:disabled {
    background-color: $md-input-background-disabled;
    color: $md-input-text-disabled;
    cursor: not-allowed;
  }

  &.cl-auto-expanding {
    resize: none;
    line-height: 1.5em;
    min-height: 30px;
    padding: 5px;
  }
}

.cl-textarea-counter {
  margin-top: 8px;
  font-size: 12px;
  color: $md-input-text-disabled;
  text-align: right;

  &.cl-textarea-counter-error {
    color: $md-error-fg;
    font-weight: 500;
  }
}
