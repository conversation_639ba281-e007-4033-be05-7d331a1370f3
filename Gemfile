# frozen_string_literal: true

source 'https://rubygems.org'

# Ruby Version
ruby '3.2.2'

# Engines
gem 'category_dojo', path: 'vendor/engines/category_dojo'
gem 'questionnaire_engine', path: 'vendor/engines/questionnaire_engine'
gem 'sim_dojo', path: 'vendor/engines/sim_dojo'
gem 'srm', path: 'vendor/engines/srm'
gem 'llm', path: 'vendor/engines/llm'

# Extensions
source 'https://rubygems.pkg.github.com/marketdojo' do
  gem 'md_exts', '0.5.5'
end

# All Environments Gems
gem 'nokogiri'
gem 'rails', '>= 7.2'
gem 'rake'
gem 'json'
gem 'sassc-rails'
gem 'bourbon'
gem 'terser'
gem 'execjs'
gem 'sprockets', '<4'
gem 'sprockets-rails'
gem 'sprockets-helpers'
# gem 'mini_racer'
gem 'remotipart', git: 'https://github.com/JangoSteve/remotipart.git'
gem 'jquery-ui-rails'
gem 'mysql2'
# AASM is used to manage state in a number of Active Record models
gem 'aasm'
# Declarative Authorization is the Authorization solution used in Market Dojo
gem 'declarative_authorization', git: 'https://github.com/marketdojo/declarative_authorization.git',
                                 branch: 'allow_rails_7'
# Delocalize is used to convert incoming values (primarily bids)
# from local format
# e.g. (German locale) 1.234,56 --becomes--> 1234.56
gem 'delocalize'
# Active merchant provides PayPal integration on our payments page
gem 'activemerchant', require: 'active_merchant'
# Delayed job queues long-running jobs our of process (e.g. sending emails).
gem 'delayed_job_active_record'
# Daemons is used by the mailman gem
gem 'daemons'
# Kaminari handles pagination of large lists of active record results lists
gem 'kaminari'
gem 'net-http'
gem 'skylight'
gem 'faraday'
gem 'faraday-httpclient'
gem 'faraday-retry'

group :production do
  # Google Cloud Platform integration
  gem 'google-cloud-logging'
  gem 'google-cloud-trace'
  gem 'google-cloud-storage'
  gem 'google-cloud-error_reporting'
  gem 'google-cloud-monitoring'
  gem 'fog-google', '~> 1.22'
  gem 'googleauth'

  # Performance and trace monitoring
  gem 'newrelic_rpm'
end

# We use Fog and Carrierwave for file uploads. We should swap this out for
# ActiveStorage as soon as practical, now that it's Rails native functionality.
gem 'fog-core', '2.2.4'
gem 'fog-json'
gem 'fog-xml'
gem 'parallel'
# Secure_Headers secures both cookies and headers of other connections
gem 'secure_headers'
gem 'bootsnap'
# GCP LLM integration
gem 'gemini-ai'
gem 'grape-swagger'
gem 'grape-entity'
gem 'grape-swagger-entity'
gem 'grape-swagger-representable'
gem 'grape-swagger-rails'
gem 'grape_logging'
gem 'devise_token_auth', '>= 1.2.0', git: 'https://github.com/lynndylanhurley/devise_token_auth'
gem 'grape_devise_token_auth'
gem 'rails-reverse-proxy'
gem 'rbnacl-libsodium'
gem 'rbnacl'

gem 'rack-cors', require: 'rack/cors'
gem 'easy_translate'
gem 'two_factor_authentication'
gem 'countries'
gem 'authtrail'
gem 'json-schema'
gem 'email_address'

group :development do
  gem 'annotaterb'
  gem 'web-console', '~> 4.2'
  # Letter Opener opens emails in browser in dev environments
  gem 'letter_opener'
  gem 'rack-mini-profiler'
  gem 'better_errors'
  gem 'derailed_benchmarks'
  gem 'syntax_tree'

  # Used in development.rb to reload code on changes
  gem 'listen'

  # Used by some scripts and tooling for codegen
  gem "unparser", "~> 0.8.0"
  gem "anbt-sql-formatter", "~> 0.1.2"
  gem "tty-table", "~> 0.12.0"
end

group :development, :test do
  # Profiling and performance tools
  # Ruby prof is used for profiling code in Jenkins
  # Pinned to avoid https://github.com/rails/rails-perftest/issues/38
  gem 'ruby-prof'
  gem 'memory_profiler'
  gem 'flamegraph'
  gem 'stackprof'
  gem 'app_profiler'
  gem 'benchmark-ips'
  gem 'rails_stats'
  gem 'debug', '>= 1.0.0'
  # Factory Bot is used in specs to build model objects and associations
  gem 'factory_bot_rails'
  gem 'binding_of_caller'
  # Roo is used to load Excel spreadsheets for specs
  gem 'roo'
  gem 'parallel_tests'
  # Ruby built from scratch may not include Readline; required for byebug
  gem 'rb-readline'
  # Pry is used for debugging
  gem 'pry'
  # For debugging [USAGE: binding.pry ]
  # 'https://github.com/deivid-rodriguez/pry-byebug'
  gem 'pry-byebug'
  gem 'test-prof'
  gem 'fasterer'
  gem 'bullet'
  gem 'dead_end'

  # Benchmark and introspect on factories,
  # for performance testing and debugging
  gem 'factory_bot_instruments'
  gem "playwright-ruby-client", "~> 1.51", require: 'playwright'
  gem 'terminal-table'
  gem 'faker'
  gem 'rainbow'
  gem 'git'
  gem 'sys-memory'
  # Overrides ActiveRecord's `to_sql` to pretty-print queries
  gem "pp_sql", "~> 2.1"
  gem 'active_record_query_trace'
end

group :test do
  # Timecop is used in time-dependent testing
  gem 'timecop'
  # Rspec - test framework
  gem 'rspec-rails'
  # Rspec mocks of various sorts; slowly replacing with Factories
  gem 'rspec-mocks'
  gem 'rspec-activemodel-mocks'
  gem 'rspec-its'
  gem 'rspec-collection_matchers'
  gem 'rspec-benchmark'
  gem 'rails-controller-testing'
  # Capybara - test framework
  gem 'capybara'
  # True headless browser driver; does not require
  gem 'poltergeist'
  gem 'capybara-screenshot'
  gem 'capybara-select2'
  # Webmock is used by VCR to mock responses from the CRM
  gem 'webmock'
  # Shoulda - extensions for testing.
  gem 'shoulda-matchers', '~> 6.2'
  gem 'shoulda-callback-matchers'
  # Webdrivers - for running tests in a browser
  gem 'webdrivers'
  gem 'should_not'
  # Static code analyzer, checks for security vulnerabilities.
  # Command: 'brakeman'
  gem 'brakeman', require: false
  # Rubocop is used to ensure coding standards are followed
  gem 'rubocop', '>=1.0.0', require: false
  gem 'rubocop-rspec', '>=2.0.0.pre', require: false
  gem 'rubocop-performance', require: false
  gem 'rubocop-rails', require: false
  gem 'rubocop-rspec_rails', require: false
  gem 'rubocop-ast', require: false
  gem 'rubocop-rake', require: false
  gem 'rubocop-capybara', require: false
  gem 'rubocop-factory_bot', require: false
  gem 'pronto'
  gem 'pronto-rubocop'
  gem 'rails_best_practices', '>=1.19'
  gem 'simplecov', require: false
  gem 'saharspec'
  gem 'temping'
end

# VCR used for testing web APIs
# Moved the VCR task out of the test group as it is
# causing problems during deployment using capistrano.
# no such file to load -- vcr/tasks/vcr.rake
gem 'vcr'
# Acts as... used to provide common extensions to Active Record models
gem 'acts_as_list' # this is monkeypatched in config/initializers/list.rb
gem 'acts_as_tree'
# TODO: Can we remove
# Dynamic form provides error message functionality on form validations.
gem 'dynamic_form', git: 'https://github.com/marketdojo/dynamic_form'
# RMagick - image processing for resizing images (ie company logos)
# the ":require => false" is here for this
# http://stackoverflow.com/questions/3606190/rmagick-warning-while-running-server
gem 'rmagick', require: false
gem 'rubyzip' # for helping reading/writing xlsx
# RubyXL for reading XLSX uploads (not we use caxlsx for writing them)
gem 'rubyXL'
# TODO: can we remove Psych?
# For processing YAML files
gem 'psych'
# Gibberish used in our integration to marketdojo-pubsub-server
# It is now deprecated and will be replaced with RbNaCl_libsodium asap
gem 'gibberish' # Cryptography library
# ya2yaml is required in lib/tasks/translate.rake, and used in vendor/engines/sim_dojo/script/translate_sim.rb
gem 'ya2yaml'
# used to provide cloning functionality in Category Dojo
gem 'deep_cloneable'
# Used to provide form functionality in Category Dojo
gem 'simple_form'
# CanCanCan is used to provide authorization in Category Dojo
# Extend to replace declarative_authorization soon, as the latter
# is deprecated
gem 'cancancan'
# Bootstrap editable rails is used to provide
# edit functionality in Category Dojo
gem 'bootstrap-editable-rails'
gem 'coffee-script' # Used to compile JS in category Dojo
gem 'simple_enum', git: 'https://github.com/felipevenancio/simple_enum.git' # Extensions to Active Record
gem 'carrierwave' # Handles uploads
# Used in Category Dojo for trees; this strongly suggests some of our data ought
# to be in a graph database (e.g. Neo4j)
gem 'acts-as-tree-with-dotted-ids',
    git: 'https://github.com/niciliketo/acts-as-tree-with-dotted-ids.git', branch: 'rails7'
gem 'wicked_pdf' # For generating PDFs. Requires wkhtmltopdf binary dependency.
# Premailer auto-generates plain text version of emails
gem 'premailer-rails'
# Nested-form is used in Advanced Lots to allow nesting
gem 'nested_form', git: 'https://github.com/ryanb/nested_form.git'
# TODO: Can we remove - Socket.io
# We use Socket IO, but think we have included js to serve instead of GEM
gem 'socket.io-rails', '~> 2.3.0'
gem 'memoist', git: 'https://github.com/wearevolt/memoist.git'
gem "html_truncator"
gem 'puma' # Now the main development server. Command: 'rails s puma -u'
# Notify about exceptions
gem 'exception_notification'
# Handle problems with different character sets (e.g. csv, xlsx uploads)
gem 'iconv'
# ActiveRecord plugin allowing us to hide and restore records
# without actually deleting them.
gem 'paranoia'
# TODO: Can we remove (added for thread safety)
gem 'i18n-timezones'
# CAXLSX used for generating XLSX files for reporting
# Used for building XLSX files. Github has a version where we can hide sheets.
gem 'caxlsx'
gem 'caxlsx_rails'
# Permits safe, sanitised evaluation of mathematical formulae from user input
gem 'dentaku'
# TODO: Remove, after switching session storage to Redis
# (Might not do this, partly depends on performance of
# Rails 5 with Redis as PubSub server and cache)
# AR session store was abstracted from core in Rails 4
gem 'activerecord-session_store'
gem 'grape', '= 2.0.0' # For API
gem 'grape-active_model_serializers'
gem 'hashie-forbidden_attributes'
# JSON Web Tokens
gem 'jwt'
gem 'shopify-money', require: 'money'
# ActiveMerchant::Integration is deprecated; the functionality is now in here.
gem 'offsite_payments'

# AK - used forked due to performance issue MD13924
gem 'acts-as-taggable-on', git: 'https://github.com/marketdojo/acts-as-taggable-on.git', branch: 'rails7'
gem 'devise'
# gem 'devise_security_extension'
gem 'devise-encryptable'
gem 'uk_postcode'
gem 'ckeditor', '~>4.3.0'
gem 'mini_magick'
# Redis for caching and ActionCable.
gem 'redis'
# gem 'redis-rack-cache'
gem 'paper_trail', '~> 15'
# For pagination on Grape API
gem 'api-pagination'
# Removed from AR core in Rails 5
gem 'activemodel-serializers-xml'
gem 'redis-activesupport'
gem 'activerecord-import'

# gem for react
gem 'shakapacker', '~> 8'
gem 'react-rails'
# For sending calendar invite in emails
gem 'icalendar'
# For checking the browser version
gem 'browser'
# Okta integration
gem 'omniauth-okta', '>= 2.0.0'
# Google integration
gem 'omniauth-google-oauth2'
# Azure OAuth2 integration
gem 'omniauth-azure-oauth2', git: 'https://github.com/marketdojo/omniauth-azure-oauth2.git'
# Generic SAML integration
gem 'omniauth-saml'
# CSRF projection for OmniAuth
gem 'omniauth-rails_csrf_protection'
# Provides ability to redirect with POST method
gem 'repost'
gem 'searchkick', '< 5'
gem 'henkei', git: 'https://github.com/marketdojo/henkei.git', branch: 'tesseract'
gem 'oj'
gem 'roo-xls'
gem 'dalli'
gem 'braintree'
gem 'ZCRMSDK', git: 'https://github.com/marketdojo/zcrm-ruby-sdk.git', branch: 'master'
# Translation
gem 'tolk'
gem 'globalize'
# Use a fork because the last commit on the original repo was in June 2022 and it appears dead
gem 'globalize-versioning', git: 'https://github.com/nourishcare/globalize-versioning', branch: 'main'
gem 'relation_to_struct'
gem 'fast_jsonapi'
gem 'fast_blank'
# Recaptcha because we get spammy registrations
gem 'recaptcha'
gem 'view_component', require: 'view_component/engine'

gem 'view_component-storybook', '~> 1.0'
gem 'turbo-rails'
# Liquid to allow us to render user defined templates
gem 'liquid'
gem 'mobility', '~> 1.3.0.rc3'
gem 'ruby_llm'

gem "active_record_union", "~> 1.3"

gem "where_exists", "~> 3.0"
