import { configureStore, createListenerMiddleware, combineSlices } from "@reduxjs/toolkit";
import { waitFor } from "@testing-library/react";

import { notificationsSlice, selectNotificationBanners } from "@/slices/notifications";
import { setupSuccessNotificationListeners } from "@/slices/successNotifications";
import qqActionTypes from "@/actions/qq/actionsTypes";

function makeStore() {
  const listener = createListenerMiddleware();
  setupSuccessNotificationListeners(listener.startListening as Parameters<typeof setupSuccessNotificationListeners>[0]);

  return configureStore({
    reducer: combineSlices(notificationsSlice),
    middleware: (gdm) => gdm().prepend(listener.middleware),
  });
}

const selectBanners = (state: unknown) =>
  selectNotificationBanners(state as Parameters<typeof selectNotificationBanners>[0]);

describe("success-notification listener", () => {
  it("dispatches a success banner when show_success is received", async () => {
    const store = makeStore();

    store.dispatch({ type: qqActionTypes.show_success, payload: "Everything is awesome!" });

    await waitFor(() =>
      expect(selectBanners(store.getState())).toContainEqual(
        expect.objectContaining({
          message: "Everything is awesome!",
          variant: "success",
          autoCloseAfter: 8_000,
        })
      )
    );
  });
});
