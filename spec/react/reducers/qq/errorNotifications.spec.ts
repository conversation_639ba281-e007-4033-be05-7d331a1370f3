import { configureStore, createListenerMiddleware, combineSlices } from "@reduxjs/toolkit";
import { waitFor } from "@testing-library/react";

import { notificationsSlice, selectNotificationBanners } from "@/slices/notifications";
import { setupErrorNotificationListeners } from "@/slices/errorNotifications";
import * as globalActionTypes from "@/actions/actionsTypes";
import qqActionTypes from "@/actions/qq/actionsTypes";

function makeStore() {
  const listener = createListenerMiddleware();
  setupErrorNotificationListeners(listener.startListening as Parameters<typeof setupErrorNotificationListeners>[0]);

  return configureStore({
    reducer: combineSlices(notificationsSlice),
    middleware: (gdm) => gdm().prepend(listener.middleware),
  });
}

const selectBanners = (state: unknown) =>
  selectNotificationBanners(state as Parameters<typeof selectNotificationBanners>[0]);

describe("error-notification listener", () => {
  const DEFAULT_MESSAGES: Record<number, string> = {
    400: "400, Bad request!",
    403: "403, Forbidden access!",
    404: "404, something went wrong!",
    409: "409, conflicts in api call!",
    500: "500, Internal server error!",
    503: "503, Service Unavailable!",
  };

  it.each(Object.entries(DEFAULT_MESSAGES))(
    "shows default banner message for HTTP %s",
    async (statusString, expectedMessage) => {
      const status = Number(statusString);
      const store = makeStore();

      store.dispatch({
        type: globalActionTypes.ERROR_CODE,
        payload: { response: { status } },
      });

      await waitFor(() =>
        expect(selectBanners(store.getState())).toContainEqual(
          expect.objectContaining({
            message: expectedMessage,
            variant: "error",
            autoCloseAfter: 10_000,
          })
        )
      );
    }
  );

  it("prefers the server-supplied error over the default", async () => {
    const store = makeStore();

    store.dispatch({
      type: globalActionTypes.ERROR_CODE,
      payload: { response: { status: 400, data: { error: "Specific backend failure" } } },
    });

    await waitFor(() =>
      expect(selectBanners(store.getState())).toContainEqual(
        expect.objectContaining({ message: "Specific backend failure" })
      )
    );
  });

  it("falls back to 'Unknown error' for an unmapped status code", async () => {
    const store = makeStore();

    store.dispatch({
      type: globalActionTypes.ERROR_CODE,
      payload: { response: { status: 418 } },
    });

    await waitFor(() =>
      expect(selectBanners(store.getState())).toContainEqual(expect.objectContaining({ message: "Unknown error" }))
    );
  });

  it("handles qqActionTypes.show_error with arbitrary payload", async () => {
    const store = makeStore();

    store.dispatch({ type: qqActionTypes.show_error, payload: "Plain string error" });

    await waitFor(() =>
      expect(selectBanners(store.getState())).toContainEqual(
        expect.objectContaining({
          message: "Plain string error",
          variant: "error",
        })
      )
    );
  });
});
